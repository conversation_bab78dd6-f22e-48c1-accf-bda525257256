"""Service for admin workspace management."""

import logging
from datetime import datetime, timed<PERSON>ta
from uuid import UUID

from sqlalchemy import and_, asc, desc, or_
from sqlalchemy.orm import Session

from ..entities.account import Account
from ..entities.audit_log import AuditLog
from ..entities.payment import Payment
from ..entities.plan import Plan
from ..entities.status import BillingCycle, SubscriptionStatus, WorkspaceStatus
from ..entities.subscription import Subscription
from ..entities.workspace import Workspace
from ..entities.workspace_member import WorkspaceMember
from ..exceptions import AccountError, DatabaseError
from ..models.admin_workspace_management import (
    AdminSubscriptionCancelRequest,
    AdminSubscriptionUpdateRequest,
    AdminWorkspaceActionResponse,
    AdminWorkspaceListResponse,
    WorkspaceDetailsResponse,
    WorkspaceFilters,
    WorkspaceListItem,
    WorkspaceSearchRequest,
    WorkspaceStatusUpdateRequest,
)
from ..models.audit_log import AuditActions, AuditTargetTypes
from ..utils.pagination import PaginationService

logger = logging.getLogger(__name__)


class AdminWorkspaceService:
    """Service for admin workspace management."""

    def __init__(self, db_session: Session):
        self.db_session = db_session
        self.pagination_service = PaginationService(db_session)

    def list_workspaces(self, search_request: WorkspaceSearchRequest | None = None) -> AdminWorkspaceListResponse:
        """
        List all workspaces with subscription information for admin.

        Supports filtering, searching, and pagination.
        """
        try:
            filters = search_request.filters if search_request else WorkspaceFilters()

            # Base query - we'll manually join data as needed
            query = self.db_session.query(Workspace)

            # Apply filters
            query = self._apply_workspace_filters(query, filters, search_request)

            # Apply sorting
            query = self._apply_sorting(query, filters)

            # Get total count for statistics
            total_count = query.count()

            # Apply pagination using the pagination service
            offset = (filters.page - 1) * filters.page_size
            workspaces = query.offset(offset).limit(filters.page_size).all()

            # Convert to response items
            workspace_items = []
            for workspace in workspaces:
                item = self._build_workspace_list_item(workspace)
                workspace_items.append(item)

            # Calculate summary statistics
            stats = self._calculate_workspace_stats()

            return AdminWorkspaceListResponse(
                total_count=total_count,
                active_count=stats["active_count"],
                trial_count=stats["trial_count"],
                expired_count=stats["expired_count"],
                workspaces=workspace_items,
            )

        except Exception as e:
            logger.exception("Failed to list workspaces")
            raise DatabaseError(message="Failed to retrieve workspace list", context={"error": str(e)}) from e

    def get_workspace_details(self, workspace_id: UUID) -> WorkspaceDetailsResponse:
        """Get detailed workspace information."""
        try:
            workspace = self._get_workspace(workspace_id)
            owner = self._get_user(workspace.owner_id)
            subscription = self._get_current_subscription(workspace_id)
            members = self._get_workspace_members(workspace_id)

            # Get plan details if subscription exists
            plan = None
            if subscription and subscription.plan_id:
                plan = self._get_plan(subscription.plan_id)

            # Build subscription details dict
            subscription_details = None
            if subscription:
                subscription_details = {
                    "subscription_id": str(subscription.id),
                    "plan_name": plan.name if plan else None,
                    "billing_cycle": subscription.billing_cycle.value,
                    "subscription_status": subscription.status.value,
                    "is_trial": subscription.is_trial,
                    "subscription_start_date": subscription.start_date.isoformat() if subscription.start_date else None,
                    "subscription_end_date": subscription.end_date.isoformat() if subscription.end_date else None,
                    "days_until_expiry": self._calculate_days_until_expiry(subscription),
                }

            return WorkspaceDetailsResponse(
                workspace_id=workspace.id,
                workspace_name=workspace.name,
                workspace_status=workspace.status,
                created_at=workspace.created_at.isoformat(),
                updated_at=(
                    workspace.updated_at.isoformat() if workspace.updated_at else workspace.created_at.isoformat()
                ),
                owner_id=workspace.owner_id,
                owner_name=owner.name if owner else None,
                owner_email=owner.email if owner else None,
                owner_status=owner.status.value if owner else None,
                subscription=subscription_details,
                members=members,
                payment_history=[],  # TODO: Implement payment history
                recent_activity=[],  # TODO: Implement recent activity
            )

        except Exception as e:
            logger.exception(f"Failed to get workspace details for {workspace_id}")
            raise DatabaseError(
                message="Failed to get workspace details", context={"workspace_id": str(workspace_id), "error": str(e)}
            ) from e

    def update_workspace_subscription(
        self, workspace_id: UUID, request: AdminSubscriptionUpdateRequest, admin_user: Account
    ) -> AdminWorkspaceActionResponse:
        """Update workspace subscription (admin action)."""
        try:
            # Get workspace and current subscription
            workspace = self._get_workspace(workspace_id)
            current_subscription = self._get_current_subscription(workspace_id)

            # Capture original state for audit
            original_state = {
                "plan_id": str(current_subscription.plan_id),
                "billing_cycle": current_subscription.billing_cycle.value,
                "end_date": current_subscription.end_date.isoformat(),
                "is_trial": current_subscription.is_trial,
            }

            # Apply action-specific updates
            if request.action == "renew":
                self._handle_renew_action(current_subscription, request)
            elif request.action == "upgrade_plan":
                self._handle_upgrade_plan_action(current_subscription, request)
            elif request.action == "trial_to_paid":
                self._handle_trial_to_paid_action(current_subscription, request)
            elif request.action == "change_billing":
                self._handle_change_billing_action(current_subscription, request)

            # Ensure subscription is active
            current_subscription.status = SubscriptionStatus.ACTIVE

            self.db_session.flush()

            # Create payment record
            payment = self._create_update_payment(current_subscription, request, admin_user)

            # Log action
            self._log_audit_action(
                action=AuditActions.SUBSCRIPTION_UPDATED,
                target_id=current_subscription.id,
                target_type=AuditTargetTypes.SUBSCRIPTION,
                actor_id=admin_user.id,
                remarks=(
                    f"Admin {request.action}: {original_state} -> "
                    f"Plan: {current_subscription.plan_id}, "
                    f"Billing: {current_subscription.billing_cycle.value}, "
                    f"End: {current_subscription.end_date} | "
                    f"Payment: {payment.reference} (${payment.amount_paid})"
                ),
            )

            self.db_session.commit()

            return AdminWorkspaceActionResponse(
                message=f"Successfully {request.action.replace('_', ' ')} subscription for workspace {workspace.name}",
                workspace_id=workspace.id,
                workspace_name=workspace.name,
                action_performed=request.action,
                performed_by=admin_user.id,
                performed_at=datetime.now().isoformat(),
                subscription_details=self._build_subscription_details(current_subscription),
                payment_details={
                    "payment_id": str(payment.id),
                    "amount": str(payment.amount_paid),
                    "reference": payment.reference,
                    "paid_at": payment.paid_at.isoformat(),
                },
            )

        except Exception as e:
            self.db_session.rollback()
            logger.exception(f"Failed to {request.action} subscription for workspace {workspace_id}")
            raise DatabaseError(
                message=f"Failed to {request.action} workspace subscription",
                context={"workspace_id": str(workspace_id), "action": request.action, "error": str(e)},
            ) from e

    def cancel_workspace_subscription(
        self, workspace_id: UUID, request: AdminSubscriptionCancelRequest, admin_user: Account
    ) -> AdminWorkspaceActionResponse:
        """Cancel workspace subscription (admin action)."""
        try:
            workspace = self._get_workspace(workspace_id)
            subscription = self._get_current_subscription(workspace_id)

            # Mark subscription as cancelled
            subscription.status = SubscriptionStatus.CANCELLED
            subscription.cancelled_at = datetime.now(UTC)
            subscription.cancellation_reason = request.reason

            self.db_session.flush()

            # Create cancellation payment record if refund is specified
            payment = None
            if request.refund_amount and request.refund_amount > 0:
                payment = Payment(
                    subscription_id=subscription.id,
                    amount_paid=-float(request.refund_amount),  # Negative for refunds
                    reference=f"refund_{subscription.id}",
                    remarks=f"Refund for cancellation: {request.reason}",
                    paid_at=datetime.now(UTC).date(),
                    created_by=admin_user.id,
                )
                self.db_session.add(payment)
                self.db_session.flush()

            # Log cancellation
            self._log_audit_action(
                action=AuditActions.SUBSCRIPTION_CANCELLED,
                target_id=subscription.id,
                target_type=AuditTargetTypes.SUBSCRIPTION,
                actor_id=admin_user.id,
                remarks=f"Admin cancellation: {request.reason} | Refund: ${request.refund_amount}",
            )

            self.db_session.commit()

            return AdminWorkspaceActionResponse(
                message=f"Successfully cancelled subscription for workspace {workspace.name}",
                workspace_id=workspace.id,
                workspace_name=workspace.name,
                action_performed="cancelled",
                performed_by=admin_user.id,
                performed_at=datetime.now().isoformat(),
                subscription_details=self._build_subscription_details(subscription),
                payment_details={
                    "payment_id": str(payment.id) if payment else None,
                    "amount": str(payment.amount_paid) if payment else "0.00",
                    "reference": payment.reference if payment else None,
                    "paid_at": payment.paid_at.isoformat() if payment else None,
                },
            )

        except Exception as e:
            self.db_session.rollback()
            logger.exception(f"Failed to cancel subscription for workspace {workspace_id}")
            raise DatabaseError(
                message="Failed to cancel workspace subscription",
                context={"workspace_id": str(workspace_id), "error": str(e)},
            ) from e

    def update_workspace_status(
        self, workspace_id: UUID, request: WorkspaceStatusUpdateRequest, admin_user: Account
    ) -> AdminWorkspaceActionResponse:
        """Update workspace status (admin action)."""
        try:
            workspace = self._get_workspace(workspace_id)
            original_status = workspace.status

            # Update workspace status
            workspace.status = request.status
            workspace.updated_at = datetime.now(UTC)

            self.db_session.flush()

            # Log status change
            self._log_audit_action(
                action=AuditActions.WORKSPACE_STATUS_CHANGED,
                target_id=workspace.id,
                target_type=AuditTargetTypes.WORKSPACE,
                actor_id=admin_user.id,
                remarks=(
                    f"Admin status change: {original_status.value} -> {request.status.value} | Reason: {request.reason}"
                ),
            )

            self.db_session.commit()

            return AdminWorkspaceActionResponse(
                message=f"Successfully updated workspace status to {request.status.value}",
                workspace_id=workspace.id,
                workspace_name=workspace.name,
                action_performed=f"status_changed_to_{request.status.value}",
                performed_by=admin_user.id,
                performed_at=datetime.now().isoformat(),
                subscription_details=None,
                payment_details=None,
            )

        except Exception as e:
            self.db_session.rollback()
            logger.exception(f"Failed to update workspace status for {workspace_id}")
            raise DatabaseError(
                message="Failed to update workspace status",
                context={"workspace_id": str(workspace_id), "error": str(e)},
            ) from e

    # Action Handlers

    def _handle_renew_action(self, subscription: Subscription, request: AdminSubscriptionUpdateRequest):
        """Handle subscription renewal."""
        extend_months = request.extend_months or 1
        days_to_add = extend_months * 30  # Approximate
        subscription.end_date = subscription.end_date + timedelta(days=days_to_add)

        # Update plan if provided
        if request.plan_id:
            subscription.plan_id = request.plan_id

    def _handle_upgrade_plan_action(self, subscription: Subscription, request: AdminSubscriptionUpdateRequest):
        """Handle plan upgrade with optional billing cycle change."""
        subscription.plan_id = request.plan_id

        # Track if billing cycle is changing
        billing_cycle_changed = False

        # Update billing cycle if provided
        if request.billing_cycle:
            old_billing_cycle = subscription.billing_cycle
            subscription.billing_cycle = request.billing_cycle
            billing_cycle_changed = old_billing_cycle != request.billing_cycle

        # If upgrading from trial, make it paid
        if subscription.is_trial:
            subscription.is_trial = False
            # Use provided billing cycle or default to monthly
            if not subscription.billing_cycle or subscription.billing_cycle == BillingCycle.TRIAL:
                subscription.billing_cycle = request.billing_cycle or BillingCycle.MONTHLY
            billing_cycle_changed = True  # Always recalculate for trial conversions

        # Recalculate end_date if billing cycle changed or converting from trial
        if billing_cycle_changed or subscription.is_trial:
            if subscription.billing_cycle == BillingCycle.MONTHLY:
                subscription.end_date = datetime.now().date() + timedelta(days=30)
            elif subscription.billing_cycle == BillingCycle.YEARLY:
                subscription.end_date = datetime.now().date() + timedelta(days=365)

    def _handle_trial_to_paid_action(self, subscription: Subscription, request: AdminSubscriptionUpdateRequest):
        """Handle trial to paid conversion."""
        subscription.is_trial = False
        subscription.billing_cycle = request.billing_cycle

        # Set new end date from today
        if request.billing_cycle == BillingCycle.MONTHLY:
            subscription.end_date = datetime.now().date() + timedelta(days=30)
        elif request.billing_cycle == BillingCycle.YEARLY:
            subscription.end_date = datetime.now().date() + timedelta(days=365)

        # Update plan if provided
        if request.plan_id:
            subscription.plan_id = request.plan_id

    def _handle_change_billing_action(self, subscription: Subscription, request: AdminSubscriptionUpdateRequest):
        """Handle billing cycle change."""
        subscription.billing_cycle = request.billing_cycle

        # Recalculate end date from today
        if request.billing_cycle == BillingCycle.MONTHLY:
            subscription.end_date = datetime.now().date() + timedelta(days=30)
        elif request.billing_cycle == BillingCycle.YEARLY:
            subscription.end_date = datetime.now().date() + timedelta(days=365)

        # Update plan if provided
        if request.plan_id:
            subscription.plan_id = request.plan_id

    # Helper Methods

    def _apply_workspace_filters(self, query, filters: WorkspaceFilters, search_request: WorkspaceSearchRequest | None):
        """Apply essential filters to workspace query."""

        # Search query (workspace name or owner email)
        if search_request and search_request.query:
            search_term = f"%{search_request.query}%"
            query = query.join(Account, Workspace.owner_id == Account.id, isouter=True).filter(
                or_(
                    Workspace.name.ilike(search_term), Account.email.ilike(search_term), Account.name.ilike(search_term)
                )
            )

        # Workspace status filter
        if filters.status:
            query = query.filter(Workspace.status == filters.status)

        return query

    def _apply_sorting(self, query, filters: WorkspaceFilters):
        """Apply sorting to workspace query."""
        sort_column = getattr(Workspace, filters.sort_by, Workspace.created_at)

        if filters.sort_order.lower() == "asc":
            return query.order_by(asc(sort_column))
        else:
            return query.order_by(desc(sort_column))

    def _build_workspace_list_item(self, workspace: Workspace) -> WorkspaceListItem:
        """Build workspace list item from workspace entity."""

        # Get owner details
        owner = None
        if workspace.owner_id:
            owner = self.db_session.query(Account).filter(Account.id == workspace.owner_id).first()

        # Get current subscription
        current_subscription = (
            self.db_session.query(Subscription)
            .filter(and_(Subscription.workspace_id == workspace.id, Subscription.is_current == True))
            .first()
        )

        # Get plan details
        plan = None
        if current_subscription and current_subscription.plan_id:
            plan = self.db_session.query(Plan).filter(Plan.id == current_subscription.plan_id).first()

        # Calculate days until expiry
        days_until_expiry = None
        if current_subscription and current_subscription.end_date:
            delta = current_subscription.end_date - datetime.now().date()
            days_until_expiry = delta.days

        # Count members
        total_members = (
            self.db_session.query(WorkspaceMember).filter(WorkspaceMember.workspace_id == workspace.id).count()
        )

        return WorkspaceListItem(
            workspace_id=workspace.id,
            workspace_name=workspace.name,
            workspace_status=workspace.status,
            created_at=workspace.created_at.isoformat(),
            owner_id=workspace.owner_id,
            owner_name=owner.name if owner else None,
            owner_email=owner.email if owner else None,
            subscription_id=current_subscription.id if current_subscription else None,
            plan_name=plan.name if plan else None,
            billing_cycle=current_subscription.billing_cycle if current_subscription else None,
            subscription_status=current_subscription.status if current_subscription else None,
            is_trial=current_subscription.is_trial if current_subscription else None,
            subscription_end_date=current_subscription.end_date if current_subscription else None,
            days_until_expiry=days_until_expiry,
            total_members=total_members,
        )

    def _calculate_workspace_stats(self) -> dict:
        """Calculate workspace statistics."""

        # Active workspaces
        active_count = self.db_session.query(Workspace).filter(Workspace.status == WorkspaceStatus.ACTIVE).count()

        # Trial subscriptions
        trial_count = (
            self.db_session.query(Subscription)
            .filter(and_(Subscription.is_current == True, Subscription.is_trial == True))
            .count()
        )

        # Expired subscriptions
        expired_count = (
            self.db_session.query(Subscription)
            .filter(and_(Subscription.is_current == True, Subscription.end_date < datetime.now().date()))
            .count()
        )

        return {"active_count": active_count, "trial_count": trial_count, "expired_count": expired_count}

    def get_trial_workspaces(self, search_request: WorkspaceSearchRequest) -> AdminWorkspaceListResponse:
        """
        Get all trial workspaces with pagination.

        Args:
            search_request: Search request with filters and pagination

        Returns:
            AdminWorkspaceListResponse: Paginated list of trial workspaces

        Raises:
            DatabaseError: If database operations fail
        """
        try:
            # Create base query for trial workspaces
            query = (
                self.db_session.query(Workspace)
                .join(Subscription, Workspace.id == Subscription.workspace_id)
                .filter(
                    and_(
                        Subscription.is_current == True,
                        Subscription.is_trial == True,
                        Workspace.status == WorkspaceStatus.ACTIVE,
                    )
                )
            )

            # Apply additional filters if provided
            filters = search_request.filters
            query = self._apply_workspace_filters(query, filters, search_request)

            # Apply pagination
            offset = (filters.page - 1) * filters.page_size
            limit = filters.page_size

            # Get total count before pagination
            total_count = query.count()

            # Apply pagination and get results
            workspaces = query.offset(offset).limit(limit).all()

            # Build workspace items
            workspace_items = []
            for workspace in workspaces:
                workspace_item = self._build_workspace_list_item(workspace)
                workspace_items.append(workspace_item)

            # Calculate stats for trial workspaces only
            stats = {
                "active_count": total_count,  # All results are active trials
                "trial_count": total_count,  # All results are trials
                "expired_count": 0,  # Filtered out expired ones
            }

            return AdminWorkspaceListResponse(
                total_count=total_count,
                active_count=stats["active_count"],
                trial_count=stats["trial_count"],
                expired_count=stats["expired_count"],
                workspaces=workspace_items,
            )

        except Exception as e:
            logger.exception("Failed to get trial workspaces")
            raise DatabaseError(f"Failed to get trial workspaces: {str(e)}")

    def get_expiring_workspaces(self, days: int, search_request: WorkspaceSearchRequest) -> AdminWorkspaceListResponse:
        """
        Get workspaces expiring within specified days with pagination.

        Args:
            days: Number of days to look ahead for expiring subscriptions
            search_request: Search request with filters and pagination

        Returns:
            AdminWorkspaceListResponse: Paginated list of expiring workspaces

        Raises:
            DatabaseError: If database operations fail
        """
        try:
            # Calculate expiry date threshold
            expiry_threshold = datetime.now().date() + timedelta(days=days)

            # Create base query for expiring workspaces
            query = (
                self.db_session.query(Workspace)
                .join(Subscription, Workspace.id == Subscription.workspace_id)
                .filter(
                    and_(
                        Subscription.is_current == True,
                        Subscription.status == SubscriptionStatus.ACTIVE,
                        Subscription.end_date <= expiry_threshold,
                        Subscription.end_date >= datetime.now().date(),  # Not already expired
                        Workspace.status == WorkspaceStatus.ACTIVE,
                    )
                )
            )

            # Apply additional filters if provided
            filters = search_request.filters
            query = self._apply_workspace_filters(query, filters, search_request)

            # Apply pagination
            offset = (filters.page - 1) * filters.page_size
            limit = filters.page_size

            # Get total count before pagination
            total_count = query.count()

            # Apply pagination and get results
            workspaces = query.offset(offset).limit(limit).all()

            # Build workspace items
            workspace_items = []
            for workspace in workspaces:
                workspace_item = self._build_workspace_list_item(workspace)
                workspace_items.append(workspace_item)

            # Calculate stats for expiring workspaces
            stats = {
                "active_count": total_count,  # All results are active
                "trial_count": len([w for w in workspace_items if w.is_trial]),
                "expired_count": 0,  # Filtered out already expired ones
            }

            return AdminWorkspaceListResponse(
                total_count=total_count,
                active_count=stats["active_count"],
                trial_count=stats["trial_count"],
                expired_count=stats["expired_count"],
                workspaces=workspace_items,
            )

        except Exception as e:
            logger.exception(f"Failed to get workspaces expiring in {days} days")
            raise DatabaseError(f"Failed to get expiring workspaces: {str(e)}")

    def _build_subscription_details(self, subscription: Subscription) -> dict:
        """Build subscription details dictionary."""
        # Get plan details
        plan = self.db_session.query(Plan).filter(Plan.id == subscription.plan_id).first()

        return {
            "subscription_id": str(subscription.id),
            "plan_id": str(subscription.plan_id),
            "plan_name": plan.name if plan else None,
            "billing_cycle": subscription.billing_cycle.value,
            "status": subscription.status.value,
            "is_trial": subscription.is_trial,
            "is_current": subscription.is_current,
            "start_date": subscription.start_date.isoformat() if subscription.start_date else None,
            "end_date": subscription.end_date.isoformat() if subscription.end_date else None,
            "days_remaining": (subscription.end_date - datetime.now().date()).days if subscription.end_date else None,
        }

    def _get_workspace(self, workspace_id: UUID) -> Workspace:
        """Get workspace by ID."""
        workspace = self.db_session.query(Workspace).filter(Workspace.id == workspace_id).first()

        if not workspace:
            raise AccountError(
                message=f"Workspace {workspace_id} not found",
                status_code=404,
                error_code="WORKSPACE_NOT_FOUND",
                context={"workspace_id": str(workspace_id)},
            )

        return workspace

    def _get_user(self, user_id: UUID) -> Account | None:
        """Get user/account by ID."""
        if not user_id:
            return None

        user = self.db_session.query(Account).filter(Account.id == user_id).first()
        return user

    def _get_plan(self, plan_id: UUID) -> Plan | None:
        """Get plan by ID."""
        if not plan_id:
            return None

        plan = self.db_session.query(Plan).filter(Plan.id == plan_id).first()
        return plan

    def _calculate_days_until_expiry(self, subscription: Subscription) -> int | None:
        """Calculate days until subscription expires."""
        if not subscription or not subscription.end_date:
            return None

        from datetime import date
        today = date.today()

        if subscription.end_date <= today:
            return 0  # Already expired or expires today

        return (subscription.end_date - today).days

    def _get_workspace_members(self, workspace_id: UUID) -> list[dict]:
        """Get workspace members with their details."""
        members = (
            self.db_session.query(WorkspaceMember, Account)
            .join(Account, WorkspaceMember.account_id == Account.id)
            .filter(WorkspaceMember.workspace_id == workspace_id)
            .all()
        )

        return [
            {
                "member_id": str(member.id),
                "account_id": str(member.account_id),
                "name": account.name,
                "email": account.email,
                "role": member.role.value,
                "joined_at": member.created_at.isoformat() if member.created_at else None,
                "status": account.status.value,
            }
            for member, account in members
        ]

    def _get_current_subscription(self, workspace_id: UUID) -> Subscription:
        """Get current active subscription for workspace."""
        subscription = (
            self.db_session.query(Subscription)
            .filter(and_(Subscription.workspace_id == workspace_id, Subscription.is_current == True))
            .first()
        )

        if not subscription:
            raise AccountError(
                message=f"No active subscription found for workspace {workspace_id}",
                status_code=404,
                error_code="SUBSCRIPTION_NOT_FOUND",
                context={"workspace_id": str(workspace_id)},
            )

        return subscription

    def _create_update_payment(
        self, subscription: Subscription, request: AdminSubscriptionUpdateRequest, admin_user: Account
    ) -> Payment:
        """Create payment record for subscription update."""

        # Calculate amount
        if request.payment_amount is not None:
            amount_paid = float(request.payment_amount)
        else:
            # Auto-calculate from plan and billing cycle
            plan = self.db_session.query(Plan).filter(Plan.id == subscription.plan_id).first()
            if subscription.is_trial or request.action == "trial_to_paid":
                # For trial conversions, use the new billing cycle
                billing_cycle = request.billing_cycle or subscription.billing_cycle
            else:
                billing_cycle = subscription.billing_cycle

            if billing_cycle == BillingCycle.MONTHLY:
                amount_paid = float(plan.price_per_month or 0.0)
            elif billing_cycle == BillingCycle.YEARLY:
                amount_paid = float(plan.price_per_year or 0.0)
            else:
                amount_paid = 0.0

        # Generate payment reference
        if request.payment_reference:
            payment_reference = request.payment_reference
        else:
            payment_reference = f"admin_{request.action}_{str(subscription.id)[:8]}"

        payment = Payment(
            subscription_id=subscription.id,
            amount_paid=amount_paid,
            reference=payment_reference,
            remarks=request.remarks or f"Admin {request.action.replace('_', ' ')}",
            paid_at=datetime.now().date(),
            created_by=admin_user.id,
        )

        self.db_session.add(payment)
        self.db_session.flush()

        return payment

    def _create_refund_payment(
        self, subscription: Subscription, request: AdminSubscriptionCancelRequest, admin_user: Account
    ) -> Payment:
        """Create refund payment record."""

        # Generate refund reference
        if request.refund_reference:
            refund_reference = request.refund_reference
        else:
            refund_reference = f"admin_refund_{str(subscription.id)[:8]}_{datetime.now().strftime('%Y%m%d')}"

        # Refunds are negative amounts
        refund_payment = Payment(
            subscription_id=subscription.id,
            amount_paid=-float(request.refund_amount),  # Negative for refund
            reference=refund_reference,
            remarks=f"Admin refund - {request.cancellation_reason}",
            paid_at=datetime.now().date(),
            created_by=admin_user.id,
        )

        self.db_session.add(refund_payment)
        self.db_session.flush()

        return refund_payment

    def _get_payment_history(self, subscription_id: UUID) -> list[dict]:
        """Get payment history for subscription."""
        payments = (
            self.db_session.query(Payment)
            .filter(Payment.subscription_id == subscription_id)
            .order_by(desc(Payment.paid_at))
            .limit(10)
            .all()
        )

        return [
            {
                "payment_id": str(payment.id),
                "amount": str(payment.amount_paid),
                "reference": payment.reference,
                "paid_at": payment.paid_at.isoformat(),
                "remarks": payment.remarks,
                "created_by": str(payment.created_by),
                "type": "refund" if payment.amount_paid < 0 else "payment",
            }
            for payment in payments
        ]

    def _get_recent_activity(self, workspace_id: UUID) -> list[dict]:
        """Get recent activity for workspace."""
        audit_logs = (
            self.db_session.query(AuditLog)
            .filter(
                or_(
                    and_(AuditLog.target_id == workspace_id, AuditLog.target_type == AuditTargetTypes.WORKSPACE),
                    and_(
                        AuditLog.target_type == AuditTargetTypes.SUBSCRIPTION,
                        AuditLog.target_id.in_(
                            self.db_session.query(Subscription.id).filter(Subscription.workspace_id == workspace_id)
                        ),
                    ),
                )
            )
            .order_by(desc(AuditLog.created_at))
            .limit(10)
            .all()
        )

        return [
            {
                "action": log.action,
                "timestamp": log.created_at.isoformat(),
                "actor_id": str(log.actor_id),
                "target_type": log.target_type,
                "success": log.success,
                "remarks": log.remarks,
            }
            for log in audit_logs
        ]

    def _log_audit_action(
        self, action: str, target_id: UUID, target_type: str, actor_id: UUID, remarks: str | None = None
    ) -> None:
        """Log audit action."""
        audit_log = AuditLog(
            action=action,
            target_id=target_id,
            target_type=target_type,
            actor_id=actor_id,
            success=True,
            remarks=remarks,
        )

        self.db_session.add(audit_log)
