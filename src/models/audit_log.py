"""Audit log models for system audit logging."""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field, field_validator

from ..utils.business_validators import (
    validate_action_field,
    validate_optional_text_field,
)


class AuditLogCreateRequest(BaseModel):
    """Request model for creating a new audit log entry."""

    actor_id: UUID | None = Field(None, description="Actor account ID (user who performed the action)")
    action: str = Field(..., min_length=1, max_length=255, description="Action performed")
    target_id: UUID = Field(..., description="Target entity ID")
    target_type: str = Field(..., min_length=1, max_length=64, description="Target entity type")
    success: bool = Field(default=True, description="Whether the action was successful")
    remarks: str | None = Field(None, max_length=1000, description="Additional remarks or error details")

    # Apply universal validators - just one line each!
    _validate_action = validate_action_field("action")
    _validate_target_type = validate_action_field("target_type")  # Same format as action
    _validate_remarks = validate_optional_text_field("remarks", 1000)


class AuditLogResponse(BaseModel):
    """Response model for audit log data."""

    model_config = {"from_attributes": True}

    id: UUID = Field(..., description="Audit log unique identifier")
    actor_id: UUID | None = Field(None, description="Actor account ID")
    action: str = Field(..., description="Action performed")
    target_id: UUID = Field(..., description="Target entity ID")
    target_type: str = Field(..., description="Target entity type")
    success: bool = Field(..., description="Whether the action was successful")
    remarks: str | None = Field(None, description="Additional remarks or error details")
    created_at: datetime = Field(..., description="Audit log creation timestamp")
    updated_at: datetime = Field(..., description="Audit log last update timestamp")
    created_by: UUID | None = Field(None, description="ID of user who created the audit log")


class AuditLogListRequest(BaseModel):
    """Request model for audit log list with filtering."""

    actor_id: UUID | None = Field(None, description="Filter by actor ID")
    action: str | None = Field(None, description="Filter by action")
    target_id: UUID | None = Field(None, description="Filter by target ID")
    target_type: str | None = Field(None, description="Filter by target type")
    success: bool | None = Field(None, description="Filter by success status")
    start_date: datetime | None = Field(None, description="Filter by start date")
    end_date: datetime | None = Field(None, description="Filter by end date")
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=50, ge=1, le=100, description="Number of items per page")

    @field_validator("action", mode="before")
    @classmethod
    def validate_action_filter(cls, v):
        """Validate action filter."""
        if v is None:
            return v

        if not v or not v.strip():
            return None

        action = v.strip().lower().replace(" ", "_").replace("-", "_")

        # Basic validation for action format
        import re

        if not re.match(r"^[a-z0-9_]+$", action):
            raise ValueError("Action filter can only contain lowercase letters, numbers, and underscores")

        return action

    @field_validator("target_type", mode="before")
    @classmethod
    def validate_target_type_filter(cls, v):
        """Validate target type filter."""
        if v is None:
            return v

        if not v or not v.strip():
            return None

        target_type = v.strip().lower()

        # Basic validation for target type format
        import re

        if not re.match(r"^[a-z0-9_]+$", target_type):
            raise ValueError("Target type filter can only contain lowercase letters, numbers, and underscores")

        return target_type


class AuditLogListResponse(BaseModel):
    """Response model for audit log list."""

    audit_logs: list[AuditLogResponse] = Field(..., description="List of audit logs")
    total: int = Field(..., description="Total number of audit logs")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of audit logs per page")
    total_pages: int = Field(..., description="Total number of pages")


class AuditLogCreateResponse(BaseModel):
    """Response model for audit log creation."""

    result: str = Field(default="success", description="Result status")
    data: AuditLogResponse = Field(..., description="Created audit log data")
    message: str = Field(default="Audit log created successfully", description="Success message")


class AuditLogStatsResponse(BaseModel):
    """Response model for audit log statistics."""

    total_logs: int = Field(..., description="Total number of audit logs")
    successful_actions: int = Field(..., description="Number of successful actions")
    failed_actions: int = Field(..., description="Number of failed actions")
    unique_actors: int = Field(..., description="Number of unique actors")
    unique_actions: int = Field(..., description="Number of unique action types")
    date_range: dict = Field(..., description="Date range of logs")


# Common audit log actions (for reference)
class AuditActions:
    """Common audit log action constants."""

    # User actions
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_REGISTER = "user_register"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"
    USER_CREATED = "user_created"  # Admin creates user

    # Admin user creation workflow
    USER_CREATION_WORKFLOW_COMPLETED = "user_creation_workflow_completed"
    USER_CREATION_WORKFLOW_FAILED = "user_creation_workflow_failed"

    # Workspace actions
    WORKSPACE_CREATE = "workspace_create"
    WORKSPACE_CREATED = "workspace_created"  # Admin creates workspace
    WORKSPACE_UPDATE = "workspace_update"
    WORKSPACE_DELETE = "workspace_delete"
    WORKSPACE_MEMBER_ADD = "workspace_member_add"
    WORKSPACE_MEMBER_ADDED = "workspace_member_added"  # Admin adds member
    WORKSPACE_MEMBER_REMOVE = "workspace_member_remove"
    WORKSPACE_MEMBER_UPDATE = "workspace_member_update"

    # Plan actions
    PLAN_CREATE = "plan_create"
    PLAN_UPDATE = "plan_update"
    PLAN_DELETE = "plan_delete"

    # Subscription actions
    SUBSCRIPTION_CREATE = "subscription_create"
    SUBSCRIPTION_CREATED = "subscription_created"  # Admin creates subscription
    SUBSCRIPTION_UPDATE = "subscription_update"
    SUBSCRIPTION_UPDATED = "subscription_updated"  # Admin updates subscription
    SUBSCRIPTION_CANCEL = "subscription_cancel"
    SUBSCRIPTION_CANCELLED = "subscription_cancelled"  # Admin cancels subscription
    SUBSCRIPTION_RENEW = "subscription_renew"
    SUBSCRIPTION_RENEWED = "subscription_renewed"  # Admin renews subscription
    SUBSCRIPTION_CONVERTED = "subscription_converted"  # Trial to paid conversion

    # Payment actions
    PAYMENT_CREATE = "payment_create"
    PAYMENT_CREATED = "payment_created"  # Admin creates payment
    PAYMENT_UPDATE = "payment_update"
    PAYMENT_PROCESS = "payment_process"
    PAYMENT_REFUND = "payment_refund"


# Common target types (for reference)
class AuditTargetTypes:
    """Common audit log target type constants."""

    ACCOUNT = "account"
    USER = "user"  # Alias for account
    WORKSPACE = "workspace"
    WORKSPACE_MEMBER = "workspace_member"
    PLAN = "plan"
    SUBSCRIPTION = "subscription"
    PAYMENT = "payment"
    AUDIT_LOG = "audit_log"
