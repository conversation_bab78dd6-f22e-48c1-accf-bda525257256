"""Unit tests for AdminWorkspaceService."""

from datetime import date, timedelta
from uuid import uuid4

import pytest

from src.entities.status import BillingCycle, SubscriptionStatus
from src.entities.subscription import Subscription
from src.models.admin_workspace_management import AdminSubscriptionUpdateRequest
from src.services.admin_workspace_service import AdminWorkspaceService


@pytest.mark.unit
class TestAdminWorkspaceService:
    """Test AdminWorkspaceService functionality."""

    def test_admin_workspace_service_creation(self, test_db_session):
        """Test that AdminWorkspaceService can be created."""
        service = AdminWorkspaceService(test_db_session)
        assert service is not None
        assert service.db_session == test_db_session

    def test_subscription_update_request_validation(self):
        """Test AdminSubscriptionUpdateRequest validation."""
        # Valid upgrade_plan request
        request = AdminSubscriptionUpdateRequest(action="upgrade_plan", plan_id=uuid4(), remarks="Test upgrade")
        assert request.action == "upgrade_plan"
        assert request.plan_id is not None

        # Valid renew request
        request = AdminSubscriptionUpdateRequest(action="renew", extend_months=3, remarks="Test renewal")
        assert request.action == "renew"
        assert request.extend_months == 3

    def test_subscription_update_request_validation_errors(self):
        """Test AdminSubscriptionUpdateRequest validation errors."""
        # Missing plan_id for upgrade_plan
        with pytest.raises(ValueError) as exc_info:
            AdminSubscriptionUpdateRequest(action="upgrade_plan", remarks="Missing plan_id")
        assert "plan_id is required" in str(exc_info.value)

        # Invalid action
        with pytest.raises(ValueError) as exc_info:
            AdminSubscriptionUpdateRequest(action="invalid_action", remarks="Invalid action")
        assert "Invalid action" in str(exc_info.value)

    def test_handle_upgrade_plan_action_basic(self, test_db_session):
        """Test basic plan upgrade action."""
        service = AdminWorkspaceService(test_db_session)

        # Create test subscription
        subscription = Subscription(
            id=uuid4(),
            workspace_id=uuid4(),
            plan_id=uuid4(),
            billing_cycle=BillingCycle.MONTHLY,
            status=SubscriptionStatus.ACTIVE,
            is_current=True,
            is_trial=False,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=30),
        )

        new_plan_id = uuid4()
        request = AdminSubscriptionUpdateRequest(action="upgrade_plan", plan_id=new_plan_id, remarks="Test upgrade")

        service._handle_upgrade_plan_action(subscription, request)

        assert subscription.plan_id == new_plan_id

    def test_handle_upgrade_plan_action_with_billing_change(self, test_db_session):
        """Test plan upgrade with billing cycle change."""
        service = AdminWorkspaceService(test_db_session)

        subscription = Subscription(
            id=uuid4(),
            workspace_id=uuid4(),
            plan_id=uuid4(),
            billing_cycle=BillingCycle.MONTHLY,
            status=SubscriptionStatus.ACTIVE,
            is_current=True,
            is_trial=False,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=30),
        )

        new_plan_id = uuid4()
        request = AdminSubscriptionUpdateRequest(
            action="upgrade_plan",
            plan_id=new_plan_id,
            billing_cycle=BillingCycle.YEARLY,
            remarks="Test upgrade to yearly",
        )

        service._handle_upgrade_plan_action(subscription, request)

        assert subscription.plan_id == new_plan_id
        assert subscription.billing_cycle == BillingCycle.YEARLY

    def test_handle_trial_conversion(self, test_db_session):
        """Test trial to paid conversion."""
        service = AdminWorkspaceService(test_db_session)

        subscription = Subscription(
            id=uuid4(),
            workspace_id=uuid4(),
            plan_id=uuid4(),
            billing_cycle=BillingCycle.TRIAL,
            status=SubscriptionStatus.ACTIVE,
            is_current=True,
            is_trial=True,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=14),
        )

        new_plan_id = uuid4()
        request = AdminSubscriptionUpdateRequest(
            action="upgrade_plan",
            plan_id=new_plan_id,
            billing_cycle=BillingCycle.YEARLY,
            remarks="Convert trial to yearly",
        )

        service._handle_upgrade_plan_action(subscription, request)

        assert subscription.plan_id == new_plan_id
        assert subscription.is_trial is False
        assert subscription.billing_cycle == BillingCycle.YEARLY

    def test_handle_renew_action(self, test_db_session):
        """Test subscription renewal action."""
        service = AdminWorkspaceService(test_db_session)

        original_end_date = date.today() + timedelta(days=30)
        subscription = Subscription(
            id=uuid4(),
            workspace_id=uuid4(),
            plan_id=uuid4(),
            billing_cycle=BillingCycle.MONTHLY,
            status=SubscriptionStatus.ACTIVE,
            is_current=True,
            is_trial=False,
            start_date=date.today(),
            end_date=original_end_date,
        )

        request = AdminSubscriptionUpdateRequest(action="renew", extend_months=3, remarks="3-month renewal")

        service._handle_renew_action(subscription, request)

        expected_end_date = original_end_date + timedelta(days=90)  # 3 months * 30 days
        assert subscription.end_date == expected_end_date

    def test_workspace_not_found_error(self, test_db_session):
        """Test workspace not found error."""
        service = AdminWorkspaceService(test_db_session)
        non_existent_id = uuid4()

        from src.exceptions import AccountError

        with pytest.raises(AccountError) as exc_info:
            service._get_workspace(non_existent_id)

        assert exc_info.value.status_code == 404
        assert "not found" in exc_info.value.message

    def test_subscription_not_found_error(self, test_db_session):
        """Test current subscription not found error."""
        service = AdminWorkspaceService(test_db_session)
        non_existent_id = uuid4()

        from src.exceptions import AccountError

        with pytest.raises(AccountError) as exc_info:
            service._get_current_subscription(non_existent_id)

        assert exc_info.value.status_code == 404
        assert "No active subscription" in exc_info.value.message

    def test_imports_work(self):
        """Test that all necessary imports work correctly."""
        # Test model imports
        from src.models.admin_workspace_management import AdminWorkspaceListResponse

        # Test router import
        from src.routes.v1.admin_workspace_management import admin_workspace_router

        # Test service import
        from src.services.admin_workspace_service import AdminWorkspaceService

        assert AdminWorkspaceListResponse is not None
        assert AdminWorkspaceService is not None
        assert admin_workspace_router is not None

    def test_api_endpoint_registration(self):
        """Test that admin workspace endpoints are registered."""
        from app import create_app

        app = create_app()

        # Check that admin workspace routes exist
        admin_routes = [route for route in app.routes if hasattr(route, "path") and "/admin/workspaces" in route.path]

        assert len(admin_routes) > 0, "Admin workspace routes should be registered"
